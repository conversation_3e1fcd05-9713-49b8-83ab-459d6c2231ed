{"name": "musicdou", "version": "1.0.0", "description": "MusicDou 是一个基于 Node.js 开发的音乐站点后端系统，采用单体架构设计，提供完整的音乐管理、用户管理、歌单管理和第三方平台集成功能。", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "docker:start": "./scripts/docker-dev.sh start", "docker:stop": "./scripts/docker-dev.sh stop", "docker:restart": "./scripts/docker-dev.sh restart", "docker:status": "./scripts/docker-dev.sh status", "docker:logs": "./scripts/docker-dev.sh logs", "docker:clean": "./scripts/docker-dev.sh clean"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "devDependencies": {"eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "nodemon": "^3.1.10", "prettier": "^3.6.2"}, "dependencies": {"bcryptjs": "^3.0.2", "connect-redis": "^9.0.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.21.2", "express-rate-limit": "^8.0.1", "express-session": "^1.18.2", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.4", "helmet": "^8.1.0", "iconv-lite": "^0.6.3", "jsonwebtoken": "^9.0.2", "minio": "^8.0.5", "mongoose": "^8.16.5", "morgan": "^1.10.1", "multer": "^2.0.2", "music-metadata": "^11.7.3", "node-fetch": "^3.3.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "redis": "^5.6.1", "sharp": "^0.34.3"}}