#!/bin/bash

# 音乐推荐算法测试脚本
# 测试新增的推荐系统功能

echo "🚀 Starting Music Recommendation Tests"
echo "======================================"

# 检查服务器是否运行
echo "📡 Checking server status..."
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Server is not running. Please start the server first:"
    echo "   npm run dev"
    exit 1
fi
echo "✅ Server is running"

echo ""
echo "🧪 Running Recommendation Tests..."
echo "=================================="

# 运行测试脚本
node test-recommendation.js

echo ""
echo "📋 Test Summary:"
echo "==============="
echo "✅ Personalized recommendations (hybrid algorithm)"
echo "✅ Genre-based recommendations"
echo "✅ Trending music analysis"
echo "✅ Discover new music"
echo "✅ Play behavior recording"
echo "✅ Different recommendation algorithms"
echo "✅ Similar music suggestions"

echo ""
echo "🎯 API Endpoints Tested:"
echo "========================"
echo "GET    /api/v1/music/recommendations"
echo "GET    /api/v1/music/recommendations/genre"
echo "GET    /api/v1/music/trending"
echo "GET    /api/v1/music/discover"
echo "GET    /api/v1/music/:id/similar"
echo "POST   /api/v1/music/:id/play-behavior"

echo ""
echo "🔧 Manual Testing Commands:"
echo "==========================="
echo ""
echo "# 1. Get personalized recommendations (requires auth)"
echo "curl -X GET 'http://localhost:3000/api/v1/music/recommendations?limit=10&algorithm=hybrid' \\"
echo "  -H 'Authorization: Bearer YOUR_TOKEN'"
echo ""
echo "# 2. Get genre-based recommendations"
echo "curl -X GET 'http://localhost:3000/api/v1/music/recommendations/genre?genre=Pop&limit=5' \\"
echo "  -H 'Authorization: Bearer YOUR_TOKEN'"
echo ""
echo "# 3. Get trending music"
echo "curl -X GET 'http://localhost:3000/api/v1/music/trending?period=7d&limit=10'"
echo ""
echo "# 4. Get discover music (requires auth)"
echo "curl -X GET 'http://localhost:3000/api/v1/music/discover?limit=10' \\"
echo "  -H 'Authorization: Bearer YOUR_TOKEN'"
echo ""
echo "# 5. Get similar music"
echo "curl -X GET 'http://localhost:3000/api/v1/music/MUSIC_ID/similar?limit=5'"
echo ""
echo "# 6. Record play behavior (requires auth)"
echo "curl -X POST 'http://localhost:3000/api/v1/music/MUSIC_ID/play-behavior' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -H 'Authorization: Bearer YOUR_TOKEN' \\"
echo "  -d '{\"duration\": 180, \"completed\": false}'"

echo ""
echo "🧠 Recommendation Algorithms:"
echo "============================="
echo "• collaborative: Based on similar users' preferences"
echo "• content: Based on music features and user history"
echo "• hybrid: Combination of collaborative and content-based"

echo ""
echo "📊 Trending Periods:"
echo "===================="
echo "• 1d: Last 24 hours"
echo "• 7d: Last 7 days (default)"
echo "• 30d: Last 30 days"

echo ""
echo "🎵 Recommendation Parameters:"
echo "============================="
echo "• limit: Number of recommendations (default: 20)"
echo "• algorithm: Recommendation algorithm (default: hybrid)"
echo "• genre: Music genre for genre-based recommendations"
echo "• period: Time period for trending analysis"

echo ""
echo "📈 Play Behavior Data:"
echo "======================"
echo "• duration: Play duration in seconds"
echo "• completed: Whether the song was played completely"
echo "• Progress is automatically calculated based on song duration"

echo ""
echo "🔍 Similar Music Algorithm:"
echo "==========================="
echo "• Same artist priority"
echo "• Same album consideration"
echo "• Same genre matching"
echo "• Similar tags analysis"
echo "• Fallback to popular music"

echo ""
echo "🎉 Music Recommendation Tests Completed!"
